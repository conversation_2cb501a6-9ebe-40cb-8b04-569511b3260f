# 🚀 Astro 文章编辑器

一个专为 Astro 博客设计的本地文章编辑器，支持 Markdown 语法、实时预览和多语言模板。

## ✨ 功能特性

### 📝 编辑功能
- **语法高亮**：支持 Markdown 和 YAML frontmatter 语法高亮
- **实时预览**：左侧编辑，右侧实时预览
- **智能补全**：自动括号匹配和缩进
- **快捷键支持**：Ctrl/Cmd + S 保存，Ctrl/Cmd + N 新建等

### 🎨 模板系统
- **博客文章模板**：标准博客文章格式
- **技术文章模板**：包含代码示例和技术结构
- **旅行文章模板**：旅行日记专用格式
- **关于页面模板**：个人介绍页面格式

### 🌍 多语言支持
- **通用版本**：所有语言显示
- **简体中文** (zh)
- **English** (en)
- **日本語** (ja)
- **繁體中文** (zh_tw)

### 🔧 快捷工具
- **插入图片**：快速插入图片链接
- **插入链接**：快速插入超链接
- **插入代码**：插入代码块
- **插入表格**：自动生成表格结构

## 🚀 使用方法

### 1. 启动编辑器
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 新建文章
1. 点击 "📝 新建文章" 按钮
2. 填写文章信息：
   - **标题**：文章标题（必填）
   - **描述**：文章描述，用于 SEO
   - **标签**：用逗号分隔的标签
   - **语言**：选择文章语言版本
   - **类型**：选择文章模板类型
3. 点击 "创建" 按钮

### 3. 编辑文章
- 左侧编辑器支持完整的 Markdown 语法
- 右侧实时预览显示渲染效果
- 顶部显示 frontmatter 信息

### 4. 保存文章
- 点击 "💾 保存文件" 按钮
- 或使用快捷键 `Ctrl/Cmd + S`
- 文件会自动下载到本地

### 5. 加载现有文章
- 点击 "📂 打开文件" 按钮
- 选择本地的 `.md` 或 `.mdx` 文件

## 📋 Frontmatter 字段说明

### 必填字段
```yaml
title: "文章标题"           # 文章标题
published: 2024-01-15T10:00:00+08:00  # 发布时间
```

### 可选字段
```yaml
description: "文章描述"      # SEO 描述
tags: ["标签1", "标签2"]    # 标签数组
lang: "zh"                  # 语言代码
abbrlink: "custom-url"      # 自定义 URL
optimizeImages: false       # 是否优化图片
toc: true                   # 是否显示目录
draft: false                # 是否为草稿
pin: 0                      # 置顶优先级
```

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl/Cmd + S` | 保存文件 |
| `Ctrl/Cmd + N` | 新建文章 |
| `Ctrl/Cmd + O` | 打开文件 |
| `Tab` | 缩进 |
| `Shift + Tab` | 取消缩进 |

## 🎯 使用技巧

### 1. 文件命名规范
编辑器会根据文章信息自动生成文件名：
- 使用 `abbrlink` 字段作为文件名
- 如果有语言设置，会添加语言后缀
- 例：`my-article-zh.md`、`tech-tutorial-en.md`

### 2. 图片处理
- **外部图片**：直接使用 URL 链接
- **本地图片**：建议放在 `public/images/` 目录，使用 `/images/photo.jpg` 引用

### 3. 代码高亮
支持多种编程语言的代码高亮：
```markdown
\`\`\`javascript
console.log('Hello, World!');
\`\`\`
```

### 4. 数学公式
支持 LaTeX 数学公式（如果你的 Astro 配置了 KaTeX）：
```markdown
$$E = mc^2$$
```

## 🔧 自定义配置

### 添加新模板
在 `app.js` 的 `templates` 对象中添加新模板：

```javascript
templates.custom = {
    name: '自定义模板',
    content: `---
title: "自定义标题"
published: ${new Date().toISOString()}
---

# 自定义内容
`
};
```

### 修改编辑器主题
在 HTML 中修改 CodeMirror 主题：
```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/dracula.min.css">
```

然后在 JavaScript 中设置：
```javascript
theme: 'dracula'
```

## 📁 文件结构

```
editor/
├── index.html          # 主页面
├── app.js             # 核心功能
└── README.md          # 说明文档
```

## 🌟 特色功能

1. **零配置**：无需安装，直接在浏览器中使用
2. **离线工作**：完全本地运行，无需网络连接
3. **实时预览**：所见即所得的编辑体验
4. **模板丰富**：多种文章类型模板
5. **多语言**：支持多语言版本管理
6. **快捷操作**：丰富的快捷工具和键盘快捷键

## 🎉 开始使用

1. 下载所有文件到本地目录
2. 在浏览器中打开 `index.html`
3. 开始创作你的第一篇文章！

享受写作的乐趣！✨

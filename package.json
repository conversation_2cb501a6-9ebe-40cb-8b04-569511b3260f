{"name": "astro-theme-retypeset", "type": "module", "version": "1.0.0", "packageManager": "bun@1.2.12", "repository": "https://github.com/radishzzz/astro-theme-retypeset", "scripts": {"dev": "bun run memos && astro check && astro dev", "dev:search": "bun run memos && astro check && astro build && bun run pagefind && astro dev", "build": "bun run memos && astro check && astro build && bun run apply-lqip && bun run pagefind", "preview": "astro preview", "astro": "astro", "lint": "eslint .", "lint:fix": "eslint . --fix", "new-post": "esno scripts/new-post.ts", "apply-lqip": "esno scripts/apply-lqip.ts", "format-posts": "esno scripts/format-posts.ts", "generate-lqip": "esno scripts/generate-lqip.ts", "update-theme": "esno scripts/update-theme.ts", "update-vendors": "curl -L https://unpkg.com/@waline/client@latest/dist/waline.css -o public/vendors/waline/waline.css && curl -L https://unpkg.com/@waline/client@latest/dist/waline.js -o public/vendors/waline/waline.js", "pagefind": "pagefind --site dist", "pagefind:dev": "astro build && pagefind --site dist", "memos": "node scripts/memos.js"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/partytown": "^2.1.4", "@astrojs/sitemap": "^3.4.1", "@waline/client": "^3.6.0", "astro": "^5.11.0", "astro-compress": "^2.3.8", "astro-og-canvas": "^0.7.0", "canvaskit-wasm": "^0.40.0", "dotenv": "^16.5.0", "feed": "^5.1.0", "googleapis": "^150.0.1", "gsap": "^3.13.0", "katex": "^0.16.22", "lite-youtube-embed": "^0.3.3", "markdown-it": "^14.1.0", "marked": "^15.0.12", "mdast-util-to-string": "^4.0.0", "node-fetch": "^3.3.2", "node-html-parser": "^7.0.1", "openai": "^5.1.0", "pagefind": "^1.3.0", "photoswipe": "^5.4.4", "reading-time": "^1.5.0", "rehype-katex": "^7.0.1", "rehype-slug": "^6.0.0", "remark-directive": "^4.0.0", "remark-math": "^6.0.0", "sanitize-html": "^2.17.0", "sharp": "^0.34.3", "twikoo": "^1.6.44", "unist-util-visit": "^5.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@astrojs/check": "^0.9.4", "@lokesh.dhakar/quantize": "^1.4.0", "@types/markdown-it": "^14.1.2", "@types/node": "^24.0.13", "@types/sanitize-html": "^2.16.0", "@unocss/eslint-plugin": "66.3.3", "@unocss/preset-attributify": "66.3.3", "@unocss/reset": "66.3.3", "astro-eslint-parser": "^1.2.2", "autocorrect-node": "^2.14.0", "eslint": "^9.31.0", "eslint-plugin-astro": "^1.3.1", "esno": "^4.8.0", "fast-glob": "^3.3.3", "lint-staged": "^16.1.2", "ndarray-pixels": "^5.0.1", "sass-embedded": "^1.89.1", "simple-git-hooks": "^2.13.0", "typescript": "~5.8.3", "unocss": "66.3.3", "unocss-preset-theme": "^0.14.1"}, "pnpm": {"patchedDependencies": {"@qwik.dev/partytown@0.11.1": "patches/@<EMAIL>"}}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{js,ts,astro}": "eslint --fix"}}
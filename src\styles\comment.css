/* Twikoo */
#twikoo svg,
#twikoo .tk-action-count {
  --at-apply: 'c-secondary/80'
}
#twikoo .tk-comments-container {
  --at-apply: 'text-sm leading-6'
}
#twikoo .tk-comments-container .tk-avatar {
  --at-apply: 'mt-0.8'
}
#twikoo .tk-avatar,
#twikoo .tk-avatar-img {
  --at-apply: 'lg:(h-12 w-12)'
}
#twikoo .tk-main .tk-avatar,
#twikoo .tk-main .tk-avatar-img {
  --at-apply: 'h-7.7 w-7.7 lg:(h-9.2 w-9.2)'
}
#twikoo .el-input__count,
#twikoo .tk-submit-action-icon.__markdown,
#twikoo .tk-action-link:has(svg path[d*="256 32C114.6 32 0 125.1"]) .tk-action-count,
#twikoo .tk-icon.__comments:has(svg path[d^="M440.65 12.57"]),
#twikoo .tk-extras,
#twikoo .tk-footer {
  --at-apply: 'hidden'
}

/* Input fields */
#twikoo .tk-meta-input {
  --at-apply: 'border border-secondary/25 rounded'
}
#twikoo .tk-meta-input .el-input + .el-input {
  --at-apply: 'ml-0 mt-0'
}
#twikoo .tk-meta-input > * {
  --at-apply: 'border-none bg-transparent'
}
#twikoo .el-input-group__prepend {
  --at-apply: 'border-none bg-transparent px-3 text-3 c-secondary'
}
#twikoo .el-input:has(input[name="mail"]) .el-input-group__prepend,
#twikoo.el-input:has(input[name="link"]) .el-input-group__prepend {
  --at-apply: 'lg:pl-0'
}
#twikoo .el-input__inner {
  --at-apply: 'my-0.5 border-none p-0 text-3'
}
#twikoo .el-input__inner::placeholder {
  --at-apply: 'c-secondary/25'
}
#twikoo .el-textarea__inner {
  --at-apply: 'border-secondary/25 py-2 leading-6'
}
#twikoo .OwO-body {
  --at-apply: 'border-secondary/25 rounded bg-background c-secondary'
}
#twikoo .OwO-item {
  --at-apply: 'hover:shadow-none'
}
#twikoo .OwO-items-show {
  --at-apply: 'overflow-x-hidden overflow-y-auto'
}
#twikoo .tk-cancel,
#twikoo .tk-preview {
  --at-apply: 'border-secondary/25 bg-transparent c-secondary font-normal active:(border-secondary/80 c-primary) hover:(border-secondary/80 c-primary)'
}
#twikoo .tk-send {
  --at-apply: 'bg-primary font-normal'
}
#twikoo .tk-send span {
  --at-apply: 'c-background'
}
#twikoo .el-button--primary {
--at-apply: 'border-none'
}

/* Margins */
#twikoo .el-loading-spinner svg {
  --at-apply: 'mx-auto'
}
#twikoo .el-loading-spinner .path {
  --at-apply: 'stroke-note'
}
#twikoo .tk-submit.tk-fade-in {
  --at-apply: 'mt-6'
}
#twikoo .tk-row.actions {
  --at-apply: 'mb-0 lg:ml-16'
}
#twikoo .tk-comments-container .tk-row.actions {
  --at-apply: 'lg:ml-14'
}
#twikoo .tk-preview-container {
  --at-apply: 'ml-14 mt-4 min-h-8 border-secondary/25 py-2 text-sm leading-6'
}
#twikoo .tk-comments-title {
  --at-apply: 'mb-0 mt-7'
}
#twikoo .tk-comment {
  --at-apply: 'mt-8'
}
#twikoo .tk-nick {
  --at-apply: 'mr-1'
}
#twikoo .tk-tag {
  --at-apply: 'mr-1 border-note rounded bg-transparent px-0.3em text-3 c-note leading-5'
}
#twikoo .tk-replies .tk-comment {
  --at-apply: 'mt-6'
}

/* Waline >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> */
#waline .wl-login-info {
  --at-apply: 'mr-3 mt-0'
}
#waline .wl-avatar {
  --at-apply: 'border-none'
}
#waline .wl-logout-btn {
  --at-apply: 'z-99'
}
#waline .wl-login-nick:not(:has(img)) {
  --at-apply: 'mt-1.4 leading-3.6';
}
#waline .wl-panel {
  --at-apply: 'm-0 border-secondary/25 rounded-lg bg-transparent'
}
#waline .wl-header {
  --at-apply: 'p-0';
}
#waline .wl-header-item {
  --at-apply: 'border-b border-primary/25 border-solid';
}
#waline .wl-header label {
  --at-apply: 'text-3';
}
#waline .wl-header input {
  --at-apply: 'text-2.8';
}
#waline .wl-card,
#waline .wl-header.item3 {
  --at-apply: 'border-b-0';
}
#waline .wl-card .wl-quote {
  --at-apply: 'mt-4 border-is-none';
}
#waline .wl-editor {
  --at-apply: 'min-h-20';
}
#waline .wl-editor::placeholder {
  --at-apply: 'c-primary/25';
}
#waline .wl-footer {
  --at-apply: 'm-2';
}
#waline .wl-text-number,
#waline .wl-action[title="Markdown Guide"],
#waline .wl-sort,
#waline .wl-gallery::-webkit-scrollbar {
  --at-apply: 'hidden';
}
#waline .wl-emoji-popup {
  --at-apply: 'start-0 max-w-532px border-secondary/25';
}
#waline .wl-emoji-popup .wl-tab-wrapper {
  scrollbar-width: thin;
}
#waline .wl-gif-popup {
  --at-apply: 'border-secondary/25';
}
#waline .wl-gif-popup input {
  --at-apply: 'border-secondary/25 bg-background';
}
#waline .wl-gif-popup input::placeholder {
  --at-apply: 'text-3.5 c-secondary/30';
}
#waline .wl-gallery {
  --at-apply: 'scrollbar-hidden';
}
#waline .wl-btn {
  --at-apply: 'duration-150 hover:border-secondary/80';
}
#waline .wl-meta-head {
  --at-apply: 'px-0 py-3';
}
#waline .wl-card-item {
  --at-apply: 'px-0 py-1';
}
#waline .wl-user-avatar {
  --at-apply: 'mt-1';
}
#waline .wl-content p {
  --at-apply: 'text-3.5 leading-6';
}
#waline .wl-time {
  --at-apply: 'c-primary/75';
}
#waline .wl-edit,
#waline .wl-delete {
  --at-apply: 'mr-0.4';
}
#waline .wl-like {
  --at-apply: 'mr-1.2';
}

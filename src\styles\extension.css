/* GitHub Card */
.gc-container {
  --at-apply: 'my-6 block overflow-x-auto uno-round-border bg-secondary/5 px-5 py-4 transition-colors ease-out hover:(bg-secondary/10 c-primary) lg:(px-6 py-5)';
  scrollbar-width: thin;
  scrollbar-color: oklch(var(--un-preset-theme-colors-secondary) / 0.15) transparent;
}
:is(h1, h2, h3, h4, h5, h6, .gc-container) + .gc-container {
  --at-apply: 'mt-4';
}
.gc-container:has(+ .gc-container) {
  --at-apply: 'mb-4';
}

/* Title Bar */
.gc-title-bar {
  --at-apply: 'flex items-center gap-2.5 lg:gap-3';
}
.gc-owner-avatar {
  --at-apply: 'aspect-square w-5.5 flex-shrink-0 rounded-full bg-secondary/20';
}
.gc-repo-title {
  --at-apply: 'flex items-center leading-normal lg:text-lg';
}
.gc-slash {
  --at-apply: 'mx-1 op-40 lg:mx-1.2';
}
.gc-github-icon {
  --at-apply: 'ml-auto w-5.5 flex-shrink-0 lg:w-6';
}

/* Repo Description */
.gc-repo-description.gc-repo-description {
  --at-apply: 'mb-3.5 mt-2.45 text-start text-sm lg:(mb-4 mt-2.8 text-base)';
}

/* Info Bar */
.gc-info-bar {
  --at-apply: 'flex items-center gap-1.75 text-xs lg:(gap-2 text-sm)';
}
.gc-info-icon {
  --at-apply: 'flex-shrink-0';
}
.gc-stars-count {
  --at-apply: 'mr-3 lg:mr-4';
}
.gc-forks-count {
  --at-apply: 'mr-3.75 lg:mr-5';
}
.gc-license-info {
  --at-apply: 'ml-0.5 mr-4';
}

/* Admonition >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> */
.admonition-title {
  --at-apply: 'mb-4 flex items-center font-semibold';
}
.admonition-title::before {
  --at-apply: 'mr-2 inline-block aspect-square w-4 align-text-bottom';
  content: '';
}

/* Note */
.admonition-note {
  --at-apply: 'border-note!';
}
.admonition-note .admonition-title {
  --at-apply: 'c-note';
}
.admonition-note .admonition-title::before {
  --at-apply: 'bg-note';
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM6.5 7.75A.75.75 0 0 1 7.25 7h1a.75.75 0 0 1 .75.75v2.75h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1 0-1.5h.25v-2h-.25a.75.75 0 0 1-.75-.75ZM8 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z'%3E%3C/path%3E%3C/svg%3E");
}

/* Tip */
.admonition-tip {
  --at-apply: 'border-tip!';
}
.admonition-tip .admonition-title {
  --at-apply: 'c-tip';
}
.admonition-tip .admonition-title::before {
  --at-apply: 'bg-tip';
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z'%3E%3C/path%3E%3C/svg%3E");
}

/* Important */
.admonition-important {
  --at-apply: 'border-important!';
}
.admonition-important .admonition-title {
  --at-apply: 'c-important';
}
.admonition-important .admonition-title::before {
  --at-apply: 'bg-important';
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .138.112.25.25.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z'%3E%3C/path%3E%3C/svg%3E");
}

/* Warning */
.admonition-warning {
  --at-apply: 'border-warning!';
}
.admonition-warning .admonition-title {
  --at-apply: 'c-warning';
}
.admonition-warning .admonition-title::before {
  --at-apply: 'bg-warning';
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z'%3E%3C/path%3E%3C/svg%3E");
}

/* Caution */
.admonition-caution {
  --at-apply: 'border-caution!';
}
.admonition-caution .admonition-title {
  --at-apply: 'c-caution';
}
.admonition-caution .admonition-title::before {
  --at-apply: 'bg-caution';
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath d='M4.47.22A.749.749 0 0 1 5 0h6c.199 0 .389.079.53.22l4.25 4.25c.141.14.22.331.22.53v6a.749.749 0 0 1-.22.53l-4.25 4.25A.749.749 0 0 1 11 16H5a.749.749 0 0 1-.53-.22L.22 11.53A.749.749 0 0 1 0 11V5c0-.199.079-.389.22-.53Zm.84 1.28L1.5 5.31v5.38l3.81 3.81h5.38l3.81-3.81V5.31L10.69 1.5ZM8 4a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 4Zm0 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z'%3E%3C/path%3E%3C/svg%3E");
}

/* Medium Embed >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> */
lite-youtube > .lty-playbtn {
  --at-apply: 'filter-none';
}
.bilibili-player {
  --at-apply: 'aspect-video w-full';
}
.twitter-tweet {
  --at-apply: 'mx-auto';
}

/* Fold */
summary {
  --at-apply: 'mb-4 w-fit cursor-pointer list-none';
}
summary::before {
  --at-apply: 'mr-2 inline-block transition-transform ease-out';
  content: '';
  border-left: 10px solid currentColor;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  transform: translate(2px, 0.5px);
}
details[open] summary::before {
  transform: rotate(90deg) translate(0.5px, -2px);
}

/* Gallery */
.gallery-container {
  --at-apply: 'my-6 flex gap-4 overflow-x-auto scrollbar-hidden';
}
.gallery-container .gallery-item {
  --at-apply: 'my-0 max-w-80% flex-shrink-0 lg:max-w-70%';
}
:is(h1, h2, h3, h4, h5, h6, .gallery-container) + .gallery-container {
  --at-apply: 'mt-4';
}
.gallery-container:has(+ .gallery-container) {
  --at-apply: 'mb-4';
}

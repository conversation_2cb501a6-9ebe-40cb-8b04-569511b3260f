// 全局变量
let editor;
let currentFileName = '';

// 文章模板
const templates = {
    daily: {
        name: '日常文章',
        content: `---
title: "文章标题"
tags: ["日常"]
lang: zh
published: ${new Date().toISOString()}
abbrlink: daily/article-name
description: "文章描述"
---

这里开始写你的日常文章内容...

## 二级标题

更多内容...

### 三级标题

- 列表项 1
- 列表项 2
- 列表项 3

**粗体文本** 和 *斜体文本*

[链接文本](https://example.com)

![图片描述](https://blog-img.shinya.click/image.jpg)
`
    },
    fiddling: {
        name: '折腾文章',
        content: `---
title: "折腾文章标题"
lang: zh
published: ${new Date().toISOString()}
tags: ["折腾"]
abbrlink: fiddling/article-name
description: "技术折腾文章描述"
---

### 前言

简要介绍这篇折腾文章的背景...

### 问题描述

遇到的问题或想要实现的功能...

### 解决方案

#### 步骤 1：环境准备

\`\`\`bash
# 安装依赖
npm install package-name
\`\`\`

#### 步骤 2：代码实现

\`\`\`javascript
// 示例代码
function example() {
    console.log('这是示例代码');
}
\`\`\`

#### 步骤 3：测试验证

\`\`\`bash
# 运行测试
npm test
\`\`\`

### 总结

总结文章的主要内容...

### 参考资料

- [参考链接 1](https://example.com)
- [参考链接 2](https://example.com)
`
    },
    travels: {
        name: '旅行文章',
        content: `---
title: "旅行标题"
subtitle: "副标题"
posttitle: "游记：旅行标题"
lang: zh
published: ${new Date().toISOString()}
coverImage: "/imgs/cover-image.webp"
abbrlink: travels/travel-name
description: "旅行描述"
days:
  - title: "第一天标题"
    descriptions:
      - "第一天的描述内容"
      - "更多描述"
    photos:
      - src: "/imgs/photo1.webp"
        alt: "照片描述"
        lat: 0.0
        lng: 0.0
        caption: "照片说明"
  - title: "第二天标题"
    descriptions:
      - "第二天的描述内容"
    photos:
      - src: "/imgs/photo2.webp"
        alt: "照片描述"
        lat: 0.0
        lng: 0.0
        caption: "照片说明"
---

这里可以写一些旅行的总体感受或者开场白...

## 行程亮点

- 亮点 1
- 亮点 2
- 亮点 3

## 实用信息

### 交通
- 如何到达
- 当地交通

### 住宿
- 酒店推荐
- 预订建议

### 美食
- 必试美食
- 餐厅推荐

## 总结

旅行的总体感受...
`
    },
    about: {
        name: '关于页面',
        content: `---
lang: zh
---

# 关于我

## 个人简介

这里写你的个人介绍...

## 技能专长

- 技能 1
- 技能 2
- 技能 3

## 工作经历

### 公司名称 (YYYY-MM - 至今)
**职位名称**

工作描述...

### 公司名称 (YYYY-MM - YYYY-MM)
**职位名称**

工作描述...

## 教育背景

### 学校名称 (YYYY-MM - YYYY-MM)
**专业名称**

相关描述...

## 项目经验

### 项目名称
**技术栈：** React, Node.js, MongoDB

项目描述...

## 联系方式

- 📧 Email: <EMAIL>
- 🐙 GitHub: [your-username](https://github.com/your-username)
- 🐦 Twitter: [@your-handle](https://twitter.com/your-handle)
- 💼 LinkedIn: [your-profile](https://linkedin.com/in/your-profile)

## 兴趣爱好

- 爱好 1
- 爱好 2
- 爱好 3
`
    },
    notes: {
        name: '笔记文章',
        content: `---
title: "笔记标题"
tags: ["笔记", "学习"]
lang: zh
published: ${new Date().toISOString()}
abbrlink: notes/note-name
description: "笔记描述"
---

# 笔记标题

## 概述

这里写笔记的概述...

## 主要内容

### 知识点 1

详细说明...

### 知识点 2

详细说明...

## 总结

总结要点...

## 参考资料

- [参考资料 1](https://example.com)
- [参考资料 2](https://example.com)
`
    },
    projects: {
        name: '项目文章',
        content: `---
title: "项目标题"
tags: ["项目"]
lang: zh
published: ${new Date().toISOString()}
abbrlink: projects/project-name
description: "项目描述"
---

# 项目标题

## 项目概述

项目的基本介绍...

## 技术栈

- 前端：React/Vue/Angular
- 后端：Node.js/Python/Java
- 数据库：MySQL/MongoDB/PostgreSQL

## 功能特性

- 功能 1
- 功能 2
- 功能 3

## 实现细节

### 核心功能实现

\`\`\`javascript
// 代码示例
function coreFunction() {
    console.log('核心功能实现');
}
\`\`\`

## 部署说明

部署相关的说明...

## 总结

项目总结...
`
    }
};

// 初始化编辑器
function initEditor() {
    editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
        mode: 'yaml-frontmatter',
        theme: 'default',
        lineNumbers: true,
        lineWrapping: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        indentUnit: 2,
        tabSize: 2,
        extraKeys: {
            'Ctrl-S': saveFile,
            'Cmd-S': saveFile,
            'Ctrl-N': newArticle,
            'Cmd-N': newArticle,
            'Ctrl-O': loadFile,
            'Cmd-O': loadFile
        }
    });

    // 监听编辑器变化
    editor.on('change', function() {
        updatePreview();
        updateStats();
        updateStatus('已修改');
    });

    // 初始化预览
    updatePreview();
    updateStats();
}

// 更新预览
function updatePreview() {
    const content = editor.getValue();
    const preview = document.getElementById('preview');
    
    // 解析 frontmatter 和 markdown
    const { frontmatter, markdown } = parseFrontmatter(content);
    
    let html = '';
    
    // 显示 frontmatter
    if (frontmatter) {
        html += `<div class="frontmatter-preview">
            <strong>📋 Frontmatter:</strong><br>
            <pre>${frontmatter}</pre>
        </div>`;
    }
    
    // 渲染 markdown
    if (markdown) {
        // 配置 marked
        marked.setOptions({
            highlight: function(code, lang) {
                return `<pre><code class="language-${lang}">${escapeHtml(code)}</code></pre>`;
            },
            breaks: true,
            gfm: true
        });
        
        html += marked.parse(markdown);
    }
    
    preview.innerHTML = html;
}

// 解析 frontmatter
function parseFrontmatter(content) {
    const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
    const match = content.match(frontmatterRegex);
    
    if (match) {
        return {
            frontmatter: match[1],
            markdown: match[2]
        };
    }
    
    return {
        frontmatter: '',
        markdown: content
    };
}

// HTML 转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 更新统计信息
function updateStats() {
    const content = editor.getValue();
    const lines = content.split('\n').length;
    const chars = content.length;
    document.getElementById('stats').textContent = `行数: ${lines} | 字符: ${chars}`;
}

// 更新状态
function updateStatus(status) {
    document.getElementById('status').textContent = status;
}

// 加载模板
function loadTemplate(type) {
    if (templates[type]) {
        editor.setValue(templates[type].content);
        updateStatus(`已加载${templates[type].name}模板`);
    }
}

// 设置语言
function setLanguage(lang) {
    const content = editor.getValue();
    const { frontmatter, markdown } = parseFrontmatter(content);
    
    if (frontmatter) {
        // 更新 frontmatter 中的 lang 字段
        const lines = frontmatter.split('\n');
        let langUpdated = false;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('lang:')) {
                lines[i] = `lang: "${lang}"`;
                langUpdated = true;
                break;
            }
        }
        
        if (!langUpdated) {
            lines.push(`lang: "${lang}"`);
        }
        
        const newContent = `---\n${lines.join('\n')}\n---\n${markdown}`;
        editor.setValue(newContent);
        updateStatus(`语言设置为: ${lang || '通用版本'}`);
    }
}

// 新建文章
function newArticle() {
    document.getElementById('newArticleModal').style.display = 'block';
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 处理新建文章表单
document.getElementById('newArticleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const title = document.getElementById('articleTitle').value;
    const description = document.getElementById('articleDescription').value;
    const tags = document.getElementById('articleTags').value.split(',').map(tag => tag.trim()).filter(tag => tag);
    const lang = document.getElementById('articleLang').value;
    const type = document.getElementById('articleType').value;
    
    // 生成文章内容
    const now = new Date().toISOString();
    const abbrlink = title.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
    
    const frontmatter = `---
title: "${title}"
published: ${now}
description: "${description}"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
lang: "${lang}"
abbrlink: "${abbrlink}"
optimizeImages: false
---`;

    const template = templates[type] || templates.daily;
    const content = template.content.replace(/^---[\s\S]*?---/, frontmatter);
    
    editor.setValue(content);
    currentFileName = `${abbrlink}${lang ? `-${lang}` : ''}.md`;
    
    closeModal('newArticleModal');
    updateStatus(`已创建新文章: ${title}`);
    
    // 清空表单
    document.getElementById('newArticleForm').reset();
});

// 加载文件
function loadFile() {
    document.getElementById('fileInput').click();
}

// 处理文件加载
document.getElementById('fileInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            editor.setValue(e.target.result);
            currentFileName = file.name;
            updateStatus(`已加载文件: ${file.name}`);
        };
        reader.readAsText(file);
    }
});

// 保存文件
function saveFile() {
    const content = editor.getValue();
    const filename = currentFileName || 'article.md';

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
    updateStatus(`已保存文件: ${filename}`);
}

// 导出文件
function exportFile() {
    const content = editor.getValue();
    const { frontmatter, markdown } = parseFrontmatter(content);

    // 解析 frontmatter 获取文件名
    let filename = 'article.md';
    if (frontmatter) {
        const titleMatch = frontmatter.match(/title:\s*["']([^"']+)["']/);
        const langMatch = frontmatter.match(/lang:\s*["']([^"']*)["']/);
        const abbrlinkMatch = frontmatter.match(/abbrlink:\s*["']([^"']+)["']/);

        if (abbrlinkMatch && abbrlinkMatch[1]) {
            filename = abbrlinkMatch[1];
        } else if (titleMatch && titleMatch[1]) {
            filename = titleMatch[1].toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
        }

        if (langMatch && langMatch[1]) {
            filename += `-${langMatch[1]}`;
        }

        filename += '.md';
    }

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
    updateStatus(`已导出文件: ${filename}`);
}

// 插入图片
function insertImage() {
    const url = prompt('请输入图片URL:', 'https://example.com/image.jpg');
    const alt = prompt('请输入图片描述:', '图片描述');

    if (url && alt) {
        const imageMarkdown = `![${alt}](${url})`;
        insertAtCursor(imageMarkdown);
    }
}

// 插入链接
function insertLink() {
    const url = prompt('请输入链接URL:', 'https://example.com');
    const text = prompt('请输入链接文本:', '链接文本');

    if (url && text) {
        const linkMarkdown = `[${text}](${url})`;
        insertAtCursor(linkMarkdown);
    }
}

// 插入代码块
function insertCode() {
    const lang = prompt('请输入代码语言 (如: javascript, python, bash):', 'javascript');
    const code = prompt('请输入代码内容:', 'console.log("Hello, World!");');

    if (lang && code) {
        const codeMarkdown = `\`\`\`${lang}\n${code}\n\`\`\``;
        insertAtCursor(codeMarkdown);
    }
}

// 插入表格
function insertTable() {
    const rows = parseInt(prompt('请输入行数:', '3'));
    const cols = parseInt(prompt('请输入列数:', '3'));

    if (rows > 0 && cols > 0) {
        let table = '';

        // 表头
        table += '|';
        for (let i = 0; i < cols; i++) {
            table += ` 列${i + 1} |`;
        }
        table += '\n';

        // 分隔线
        table += '|';
        for (let i = 0; i < cols; i++) {
            table += ' --- |';
        }
        table += '\n';

        // 数据行
        for (let i = 0; i < rows - 1; i++) {
            table += '|';
            for (let j = 0; j < cols; j++) {
                table += ` 数据${i + 1}-${j + 1} |`;
            }
            table += '\n';
        }

        insertAtCursor(table);
    }
}

// 在光标位置插入文本
function insertAtCursor(text) {
    const cursor = editor.getCursor();
    editor.replaceRange(text, cursor);
    editor.focus();
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // 阻止默认的保存行为
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveFile();
    }

    // 新建文章快捷键
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        newArticle();
    }

    // 打开文件快捷键
    if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        loadFile();
    }
});

// 点击模态框外部关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initEditor();
    updateStatus('编辑器已就绪');

    // 加载默认模板
    loadTemplate('daily');
});

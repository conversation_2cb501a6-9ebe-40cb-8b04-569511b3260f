---
title: macOS 26 / iPadOS 26 Early Experience
tags: ["tinkering","macos","ipados","Liquid Glass"]
lang: en
published: 2025-06-10T20:18:00+08:00
abbrlink: fiddling/macos-26-trial
description: "macOS and iPadOS 26 introduce the brand-new Liquid Glass design language, refreshing the interface style with more modern icons and window effects. Especially notable is the windowed app mode on iPad, marking its shift toward a productivity tool. However, some design choices, like the transparent Control Center and the integration of Launchpad, have sparked controversy, indicating that user experience still needs refinement. Despite its flaws, this update lays a solid foundation for future development and is worth looking forward to."
---

Another year, another tech gala: WWDC 25 was held in the early hours today. As is well known, technology is fundamentally about re-skinning. Besides the highlight on Apple Intelligence—which, frankly, is irrelevant to Chinese users—the biggest updates are the release of the latest versions of all platform systems unified under year-based naming, along with the introduction of the Liquid Glass design language.

Originally, I wasn’t particularly interested in WWDC and usually wait conservatively for the official release. But this morning, with the flood of tech bloggers’ reviews, news, and countless memes mocking the event, it was hard not to pay attention. After a quick overview, I decided to update and try it out.

![Supposedly, Liquid Glass was inspired by this](https://blog-img.shinya.click/2025/10a1a04fe5272425d5df103b0403286b.png)

Since I’ve already sold my iPhone (see [previous post](/en/fiddling/one-month-using-android)), this time my experience is limited to macOS and iPadOS—both of which seem to have drawn relatively less criticism.

The most immediate impression is, of course, the UI changes. The new multi-platform unified design language, Liquid Glass, is said to have originated from Vision OS. I’ve sold my Vision OS device too, so I can’t compare directly, but intuitively, it really carries that Vision OS vibe: windows use a frosted glass effect, and icons have all been revamped into a skeuomorphic (frosted glass slice?) style—let’s just call it that.

![Honestly, it feels a bit knockoff, like an ancient counterfeit phone vibe](https://blog-img.shinya.click/2025/a8cf24e6e65fb60e3b12459b13dd7b80.png)

On the iPad, the unified icon style works quite well; third-party app icons are also adapted to match, giving a very cohesive look.

![iPad icons](https://blog-img.shinya.click/2025/7c1c0ec885660f91df41053898c0cd88.PNG)

Windows have also been updated to a unified style. Additionally, Apple Music’s visual design has been unified and looks more high-tech, unlike the old iTunes style that felt clunky (though actual experience may vary).

![Unified windows are quite pleasing to the eye](https://blog-img.shinya.click/2025/7b73e36fb041c8dc5e7ff9aad0faeb57.png)

On macOS, the biggest backlash isn’t really about Liquid Glass (most UI complaints come from iPhone users), but rather the removal of Launchpad. Its functionality has been merged into Spotlight: clicking the App icon in the Dock or pressing Cmd + Space now brings up the Spotlight search box.

![Spotlight](https://blog-img.shinya.click/2025/4b190a366a67d3f5dc14bea1491ebb92.png)

You have to press Cmd + 1 again to display all apps.

![All Apps page](https://blog-img.shinya.click/2025/9b74ffbd1b34ef15ef5b320135c653d7.png)

This design is a bit problematic, especially for someone like me with early-stage memory issues—I simply can’t remember app names and have to rely on icons to recall their functions. I wonder what grudge Apple holds against Launchpad. Maybe in a few days, some indie developer will release a Launchpad replacement and charge me $99.

Quiz: What’s the Photos app called—“Photos” or “Gallery”?

Answer: Photos (I searched several times and kept getting it wrong)

Another minor update is that pressing the volume or brightness keys on the keyboard no longer triggers a fullscreen overlay; instead, a small popup appears in the status bar, somewhat like the logic on touchscreens.

![Volume control](https://blog-img.shinya.click/2025/85841f6a20fe5bc87d2e4e7036e7e1ec.png)
![Brightness control](https://blog-img.shinya.click/2025/c781da9ec7dfac96e7588a8faa047df5.png)

Without an iPhone, the most classic Liquid Glass experience I can get is on the iPad. My iPad is the 11-inch iPad Pro, 4th generation, running on the M2 chip.

I skipped the flashy lock screen clock stretching features and focused mainly on the notification center and Control Center. Pulling down the notification center reveals a real-time glass effect, as if you’re dragging a piece of glass—very stunning. I’m just not sure about the performance requirements and whether older iPads can handle it smoothly.

![Notice the edge effect](https://blog-img.shinya.click/2025/acbdd751ce6b1ca0c76301c826274aea.PNG)

The most criticized part is the Control Center, where the transparency is so high that it’s hard to distinguish elements. I guess future versions might tweak this or allow user customization.

![It’s a bit hard to see](https://blog-img.shinya.click/2025/e4cd043a2db8acf7c8a5d12f3c90065e.PNG)

Of course, this update isn’t without merit. The biggest highlight is the windowed app mode on iPadOS. Previously, iPad apps could only run fullscreen or in simple split-screen multitasking, with only one app occupying the screen or sharing it in a limited way. Now, the windowed app logic is completely aligned with macOS: you can freely resize, maximize, minimize windows, and have them overlap. With an external large display, the iPad can fully function as a computer, marking a significant step toward becoming a true productivity tool.

![No hype, no fluff—this is genuinely Legendary](https://blog-img.shinya.click/2025/4846d5e04b7811a0435573f43d26dfec.PNG)

These are my initial impressions of this update. Honestly, I’m quite optimistic about this design language. Windows Vista, limited by hardware at the time, failed to realize this vision of the future—Apple has finally achieved it. Some serious issues and complaints will likely be addressed in future updates. This is only the first developer preview of macOS 26 / iPadOS 26, and the future looks promising.
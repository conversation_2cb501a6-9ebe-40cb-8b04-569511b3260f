@echo off
title Astro Article Editor

echo.
echo Starting Astro Article Editor...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found
    echo Please install Python 3.x first
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check required files
if not exist "index.html" (
    echo Error: index.html file not found
    pause
    exit /b 1
)

if not exist "app.js" (
    echo Error: app.js file not found
    pause
    exit /b 1
)

echo Environment check passed
echo Starting local server...
echo.

REM Start Python server
python server.py

pause

@echo off
chcp 65001 >nul
title Astro 文章编辑器

echo.
echo 🚀 正在启动 Astro 文章编辑器...
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到 Python
    echo 请先安装 Python 3.x 版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "index.html" (
    echo ❌ 错误：找不到 index.html 文件
    pause
    exit /b 1
)

if not exist "app.js" (
    echo ❌ 错误：找不到 app.js 文件
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo 📍 启动本地服务器...
echo.

REM 启动 Python 服务器
python server.py

pause

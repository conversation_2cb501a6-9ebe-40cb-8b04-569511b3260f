#!/bin/bash

# Astro 文章编辑器启动脚本

echo ""
echo "🚀 正在启动 Astro 文章编辑器..."
echo ""

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误：未找到 Python"
        echo "请先安装 Python 3.x 版本"
        echo "macOS: brew install python3"
        echo "Ubuntu: sudo apt install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查必要文件
if [ ! -f "index.html" ]; then
    echo "❌ 错误：找不到 index.html 文件"
    exit 1
fi

if [ ! -f "app.js" ]; then
    echo "❌ 错误：找不到 app.js 文件"
    exit 1
fi

echo "✅ 环境检查通过"
echo "📍 启动本地服务器..."
echo ""

# 启动 Python 服务器
$PYTHON_CMD server.py

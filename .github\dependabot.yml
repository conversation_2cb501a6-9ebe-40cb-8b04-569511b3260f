version: 2
updates:
  - package-ecosystem: npm
    directory: /
    schedule:
      interval: daily
    open-pull-requests-limit: 3
    pull-request-branch-name:
      separator: '-'
    groups:
      patch-updates:
        patterns:
          - '*'
        update-types:
          - patch
      minor-updates:
        patterns:
          - '*'
        update-types:
          - minor
    ignore:
      - dependency-name: '*'
        update-types: ['version-update:semver-major']

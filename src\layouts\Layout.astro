---
import Button from '@/components/Button.astro'
import Footer from '@/components/Footer.astro'
import Header from '@/components/Header.astro'
import Navbar from '@/components/Navbar.astro'
import CodeCopyButton from '@/components/Widgets/CodeCopyButton.astro'
import GithubCard from '@/components/Widgets/GithubCard.astro'
import ImageZoom from '@/components/Widgets/ImageZoom.astro'
import MediaEmbed from '@/components/Widgets/MediaEmbed.astro'
import SoundEffect from '@/components/Widgets/SoundEffect.astro'
import themeConfig from '@/config'
import Head from '@/layouts/Head.astro'
import { getPageInfo } from '@/utils/page'
import '@/styles/comment.css'
import '@/styles/extension.css'
import '@/styles/font.css'
import '@/styles/global.css'
import '@/styles/lqip.css'
import '@/styles/markdown.css'
import '@/styles/transition.css'
import '@/styles/code-collapse.css'

interface Props {
  postTitle?: string
  postDescription?: string
  postSlug?: string
  supportedLangs?: string[]
}

const { postTitle, postDescription, postSlug, supportedLangs = [] } = Astro.props
const { isPost } = getPageInfo(Astro.url.pathname)
const fontStyle = themeConfig.global.fontStyle === 'serif' ? 'font-serif' : 'font-sans'
const MarginBottom = isPost && themeConfig.comment?.enabled
  ? 'mb-10' // Post page with comments
  : 'mb-12' // Other pages without comments
---

<html
  lang={Astro.currentLocale}
  class:list={[
    fontStyle,
    { 'scroll-smooth': isPost },
  ]}
>
  <Head {postTitle} {postDescription} {postSlug} />
  <body>
    <div
      class="mx-auto max-w-205.848 min-h-vh w-full min-h-dvh"
      p="x-[min(7.25vw,3.731rem)] y-10"
      lg="mx-[max(5.75rem,calc(50vw-34.25rem))] my-20 max-w-[min(calc(75vw-16rem),44rem)] min-h-full p-0"
    >
      <Header />
      <Navbar />
      <main class={MarginBottom}>
        <slot />
      </main>
      <Footer />
    </div>
    <Button supportedLangs={supportedLangs} />
    <SoundEffect />
    <CodeCopyButton />
    <GithubCard />
    <MediaEmbed />
    <ImageZoom />
  </body>
</html>

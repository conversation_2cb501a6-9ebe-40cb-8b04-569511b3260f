---
import LanguageSwitcherIcon from '@/assets/icons/language-switcher.svg'
import ThemeToggleIcon from '@/assets/icons/theme-toggle.svg'
import SearchIcon from '@/assets/icons/search.svg'
import Search from '@/components/Widgets/Search.astro'
import { moreLocales, themeConfig } from '@/config'
import { getNextGlobalLangPath, getNextSupportedLangPath, getTagsListLangPath } from '@/i18n/path'
import { isPostPage, isTagPage } from '@/utils/page'
import { getLangFromPath } from "@/i18n/lang";
import { ui } from "@/i18n/ui";

interface Props {
  supportedLangs: string[]
}

const {
  light: { background: lightMode },
  dark: { background: darkMode },
} = themeConfig.color

const { supportedLangs } = Astro.props
const currentPath = Astro.url.pathname
const isPost = isPostPage(currentPath)
const isTag = isTagPage(currentPath)

// 获取当前语言和UI文案
const currentLang = getLangFromPath(currentPath);
const currentUI = ui[currentLang as keyof typeof ui] ?? ui.zh;

// Check if there are other languages to switch
const showLanguageSwitcher = moreLocales.length > 0
// Choose a language switch list according to the page type
const nextUrl = isTag
  ? getTagsListLangPath(currentPath) // Switch between all languages in tag pages
  : isPost
    ? getNextSupportedLangPath(currentPath, supportedLangs) // Switch between supported languages in post pages
    : getNextGlobalLangPath(currentPath) // Switch between all languages in other pages
---

<div
  class:list={[
  'absolute right-7.25vw top-14.6 flex gap-6 min-[823px]:max-lg:right-[calc(50vw-22rem)]',
  'lg:(fixed bottom-[min(10.27rem+1.92vw,12rem)] right-[max(5rem,calc(50vw-35rem))] top-auto w-14rem)',
]}
>
  <!-- Search -->
  <button
    id="search-button"
    aria-label={currentUI.search.ariaLabel}
    class="aspect-square w-4 c-secondary active:scale-90 hover:c-primary"
  >
    <SearchIcon
      aria-hidden="true"
      fill="currentColor"
    />
  </button>

  <!-- Language Switcher -->
  {showLanguageSwitcher && (
    <a
      id="language-switcher"
      href={nextUrl}
      class="aspect-square w-4 c-secondary active:scale-90 hover:c-primary"
      aria-label="Switch website language"
    >
      <LanguageSwitcherIcon
        aria-hidden="true"
        fill="currentColor"
      />
    </a>
  )}

  <!-- Theme Toggle -->
  <button
    id="theme-toggle-button"
    class="aspect-square w-4 c-secondary active:scale-90 hover:c-primary"
    aria-label="Switch light/dark theme"
  >
    <ThemeToggleIcon
      aria-hidden="true"
      fill="currentColor"
    />
  </button>
</div>

<!-- Search Component -->
<Search />

<!-- Theme Toggle Script >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> -->
<script
  is:inline
  define:vars={{
    lightMode,
    darkMode,
 }}
>
// Apply theme changes
function applyTheme(isDark) {
  document.documentElement.classList.toggle('dark', isDark)

  // Update meta theme-color tag
  const metaThemeColor = document.head.querySelector('meta[name="theme-color"]')
  if (metaThemeColor && lightMode && darkMode) {
    metaThemeColor.setAttribute('content', isDark ? darkMode : lightMode)
  }

  localStorage.setItem('theme', isDark ? 'dark' : 'light')
  document.dispatchEvent(new Event('theme-changed'))
}

// Toggle theme mode
function toggleTheme() {
  const isDark = !document.documentElement.classList.contains('dark')
  applyTheme(isDark)
}

// Handle theme toggle click events
function handleThemeToggleClick(e) {
  const target = e.target instanceof Element ? e.target : null
  const button = target?.closest('#theme-toggle-button')

  if (!button) {
    return
  }

  // If reduceMotion is enabled, update theme directly
  if (document.documentElement.classList.contains('reduce-motion')) {
    toggleTheme()
    return
  }

  // Add temporary markers before view transition
  document.documentElement.style.setProperty('view-transition-name', 'animation-theme-toggle')
  document.documentElement.setAttribute('data-theme-changing', '')

  // Use View Transitions API for theme toggle
  const transition = document.startViewTransition(toggleTheme)

  // Remove temporary markers after view transition
  transition.finished.then(() => {
    document.documentElement.style.removeProperty('view-transition-name')
    document.documentElement.removeAttribute('data-theme-changing')
  })
}

// Bind click event to the button
document.addEventListener('click', handleThemeToggleClick, { passive: true })

// Listen to system theme changes in real time
window.matchMedia('(prefers-color-scheme: dark)')
  .addEventListener('change', (event) => {
    const isDark = event.matches
    // Apply system theme preference
    applyTheme(isDark)
  })
</script>

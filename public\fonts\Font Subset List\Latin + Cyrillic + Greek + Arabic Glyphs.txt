<!-- https://github.com/googlefonts/glyphsets/blob/main/GLYPHSETS.md -->

### GF Latin Kernel

Letter (52 glyphs):
`A B C D E F G H I J K L M N O P Q R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z`

Mark, spacing (1 glyphs):
`/grave`

Number (10 glyphs):
`0 1 2 3 4 5 6 7 8 9`

Punctuation (29 glyphs):
`! " # ' ( ) * , - . / : ; ? [ \ ] _ { } · – — ‘ ’ “ ” • …`

Separator (2 glyphs):
`   `

Symbol (22 glyphs):
`$ % & + < = > @ ^ | ~ ¢ £ ¥ © ® ° × ÷ € ™ −`



### GF Latin Core

Letter (220 glyphs):
`A B C D E F G H I J K L M N O P Q R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z ª º À Á Â Ã Ä Å Æ Ç È É Ê Ë Ì Í Î Ï Ð Ñ Ò Ó Ô Õ Ö Ø Ù Ú Û Ü Ý Þ ß à á â ã ä å æ ç è é ê ë ì í î ï ð ñ ò ó ô õ ö ø ù ú û ü ý þ ÿ Ā ā Ă ă Ą ą Ć ć Ċ ċ Č č Ď ď Đ đ Ē ē Ė ė Ę ę Ě ě Ğ ğ Ġ ġ Ģ ģ Ħ ħ Ī ī Į į İ ı Ķ ķ Ĺ ĺ Ļ ļ Ľ ľ Ł ł Ń ń Ņ ņ Ň ň Ő ő Œ œ Ŕ ŕ Ř ř Ś ś Ş ş Š š Ť ť Ū ū Ů ů Ű ű Ų ų Ŵ ŵ Ŷ ŷ Ÿ Ź ź Ż ż Ž ž Ș ș Ț ț ȷ Ẁ ẁ Ẃ ẃ Ẅ ẅ ẞ Ỳ ỳ /idotaccent`

Mark, nonspacing (15 glyphs):
`◌̀ ◌́ ◌̂ ◌̃ ◌̄ ◌̆ ◌̇ ◌̈ ◌̊ ◌̋ ◌̌ ◌̦ ◌̧ ◌̨ ◌/caroncomb.alt`

Mark, spacing (13 glyphs):
`/grave ¨ ¯ ´ ¸ ˆ ˇ ˘ ˙ ˚ ˛ ˜ ˝`

Number (10 glyphs):
`0 1 2 3 4 5 6 7 8 9`

Punctuation (39 glyphs):
`! " # ' ( ) * , - . / : ; ? [ \ ] _ { } ¡ « · » ¿ – — ‘ ’ ‚ “ ” „ • … ‹ › /periodcentered.loclCAT /periodcentered.loclCAT.case`

Separator (3 glyphs):
`    /.notdef`

Symbol (24 glyphs):
`$ % & + < = > @ ^ | ~ ¢ £ ¥ § © ® ° ¶ × ÷ € ™ −`



### GF Cyrillic Core

Letter (166 glyphs):
`A B C D E F G H I J K L M N O P Q R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z Ё Ђ Є І Ї Ј Љ Њ Ћ Ў Џ А Б В Г Д Е Ж З И Й К Л М Н О П Р С Т У Ф Х Ц Ч Ш Щ Ъ Ы Ь Э Ю Я а б в г д е ж з и й к л м н о п р с т у ф х ц ч ш щ ъ ы ь э ю я ё ђ є і ї ј љ њ ћ ў џ Ґ ґ Ғ ғ Җ җ Қ қ Ң ң Ү ү Ұ ұ Ҳ ҳ Ҷ ҷ Һ һ Ә ә Ӣ ӣ Ө ө Ӯ ӯ`

Mark, nonspacing (5 glyphs):
`◌̀ ◌́ ◌̄ ◌̆ ◌̈`

Mark, spacing (2 glyphs):
`/grave ʼ`

Number (10 glyphs):
`0 1 2 3 4 5 6 7 8 9`

Punctuation (33 glyphs):
`! " # ' ( ) * , - . / : ; ? [ \ ] _ { } « · » – — ‘ ’ ‚ “ ” „ • …`

Separator (2 glyphs):
`   `

Symbol (23 glyphs):
`$ % & + < = > @ ^ | ~ ¢ £ ¥ © ® ° × ÷ € № ™ −`



### GF Greek Core

Letter (123 glyphs):
`A B C D E F G H I J K L M N O P Q R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z Ά Έ Ή Ί Ό Ύ Ώ ΐ Α Β Γ Δ Ε Ζ Η Θ Ι Κ Λ Μ Ν Ξ Ο Π Ρ Σ Τ Υ Φ Χ Ψ Ω Ϊ Ϋ ά έ ή ί ΰ α β γ δ ε ζ η θ ι κ λ μ ν ξ ο π ρ ς σ τ υ φ χ ψ ω ϊ ϋ ό ύ ώ Ϗ ϗ`

Mark, nonspacing (2 glyphs):
`◌́ ◌̈`

Mark, spacing (3 glyphs):
`/grave ΄ ΅`

Number (10 glyphs):
`0 1 2 3 4 5 6 7 8 9`

Punctuation (33 glyphs):
`! " # ' ( ) * , - . / : ; ? [ \ ] _ { } « · » ; · – — ‘ ’ “ ” • …`

Separator (2 glyphs):
`   `

Symbol (24 glyphs):
`$ % & + < = > @ ^ | ~ ¢ £ ¥ © ® ° × ÷ ʹ ͵ € ™ −`



### GF Arabic Core

Letter (239 glyphs):
`A B C D E F G H I J K L M N O P Q R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z ء آ أ ؤ إ ئ ا ب ة ت ث ج ح خ د ذ ر ز س ش ص ض ط ظ ع غ ـ ف ق ك ل م ن ه و ى ي ٮ ٯ ٱ ٹ پ چ ڈ ڑ ژ ڡ ک گ ں ھ ہ ی ے ݣ /ain-ar.fina /ain-ar.init /ain-ar.medi /alef-ar.fina /alefHamzaabove-ar.fina /alefHamzabelow-ar.fina /alefMadda-ar.fina /alefMaksura-ar.fina /alefMaksura-ar.init /alefMaksura-ar.medi /alefWasla-ar.fina /beh-ar.fina /beh-ar.init /beh-ar.medi /behDotless-ar.fina /behDotless-ar.init /behDotless-ar.medi /dad-ar.fina /dad-ar.init /dad-ar.medi /dal-ar.fina /ddal-ar.fina /feh-ar.fina /feh-ar.init /feh-ar.medi /fehDotless-ar.fina /fehDotless-ar.init /fehDotless-ar.medi /gaf-ar.fina /gaf-ar.init /gaf-ar.medi /ghain-ar.fina /ghain-ar.init /ghain-ar.medi /hah-ar.fina /hah-ar.init /hah-ar.medi /heh-ar.fina /heh-ar.init /heh-ar.medi /hehDoachashmee-ar.fina /hehDoachashmee-ar.init /hehDoachashmee-ar.medi /hehgoal-ar.fina /hehgoal-ar.init /hehgoal-ar.medi /jeem-ar.fina /jeem-ar.init /jeem-ar.medi /jeh-ar.fina /kaf-ar.fina /kaf-ar.init /kaf-ar.medi /keheh-ar.fina /keheh-ar.init /keheh-ar.medi /kehehThreedotsabove-ar.fina /kehehThreedotsabove-ar.init /kehehThreedotsabove-ar.medi /khah-ar.fina /khah-ar.init /khah-ar.medi /lam-ar.fina /lam-ar.init /lam-ar.medi /lam_alef-ar /lam_alefHamzaabove-ar /lam_alefHamzabelow-ar /lam_alefMadda-ar /lam_alefWasla-ar /meem-ar.fina /meem-ar.init /meem-ar.medi /noon-ar.fina /noon-ar.init /noon-ar.medi /noonghunna-ar.fina /noonghunna-ar.init /noonghunna-ar.medi /peh-ar.fina /peh-ar.init /peh-ar.medi /qaf-ar.fina /qaf-ar.init /qaf-ar.medi /qafDotless-ar.fina /qafDotless-ar.init /qafDotless-ar.medi /reh-ar.fina /rreh-ar.fina /sad-ar.fina /sad-ar.init /sad-ar.medi /seen-ar.fina /seen-ar.init /seen-ar.medi /sheen-ar.fina /sheen-ar.init /sheen-ar.medi /tah-ar.fina /tah-ar.init /tah-ar.medi /tcheh-ar.fina /tcheh-ar.init /tcheh-ar.medi /teh-ar.fina /teh-ar.init /teh-ar.medi /tehMarbuta-ar.fina /thal-ar.fina /theh-ar.fina /theh-ar.init /theh-ar.medi /tteh-ar.fina /tteh-ar.init /tteh-ar.medi /waw-ar.fina /wawHamzaabove-ar.fina /yeh-ar.fina /yeh-ar.init /yeh-ar.medi /yehFarsi-ar.fina /yehFarsi-ar.init /yehFarsi-ar.medi /yehHamzaabove-ar.fina /yehHamzaabove-ar.init /yehHamzaabove-ar.medi /yehbarree-ar.fina /zah-ar.fina /zah-ar.init /zah-ar.medi /zain-ar.fina`

Mark, nonspacing (41 glyphs):
`◌ؕ ◌ً ◌ٌ ◌ٍ ◌َ ◌ُ ◌ِ ◌ّ ◌ْ ◌ٓ ◌ٔ ◌ٕ ◌ٖ ◌٘ ◌ٰ ◌ۛ ◌/dotabove-ar ◌/dotbelow-ar ◌/dotcenter-ar ◌/gafsarkashabove-ar ◌/hamzaaboveDamma-ar ◌/hamzaaboveDammatan-ar ◌/hamzaaboveFatha-ar ◌/hamzaaboveFathatan-ar ◌/hamzaaboveSukun-ar ◌/hamzabelowKasra-ar ◌/hamzabelowKasratan-ar ◌/shaddaAlefabove-ar ◌/shaddaDamma-ar ◌/shaddaDammatan-ar ◌/shaddaFatha-ar ◌/shaddaFathatan-ar ◌/shaddaKasra-ar ◌/shaddaKasratan-ar ◌/threedotsdownabove-ar ◌/threedotsdownbelow-ar ◌/threedotsdowncenter-ar ◌/threedotsupabove-ar ◌/twodotshorizontalabove-ar ◌/twodotshorizontalbelow-ar ◌/wasla-ar`

Mark, spacing (1 glyphs):
`/grave`

Number (34 glyphs):
`0 1 2 3 4 5 6 7 8 9 ٠ ١ ٢ ٣ ٤ ٥ ٦ ٧ ٨ ٩ ٫ ٬ ۰ ۱ ۲ ۳ ۴ ۵ ۶ ۷ ۸ ۹ /fourFarsi-ar.urdu /sevenFarsi-ar.urdu`

Punctuation (41 glyphs):
`! " # ' ( ) * , - . / : ; ? [ \ ] _ { } « · » ، ؍ ؛ ؟ ٭ ۔ – — ‘ ’ “ ” • … ‹ › ﴾ ﴿`

Separator (2 glyphs):
`   `

Symbol (25 glyphs):
`$ % & + < = > @ ^ | ~ ¢ £ ¥ © ® ° × ÷ ؉ ٪ € ™ − ◌`

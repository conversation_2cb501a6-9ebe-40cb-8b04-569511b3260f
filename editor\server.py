#!/usr/bin/env python3
"""
简单的本地服务器，用于运行 Astro 文章编辑器
使用方法：python server.py
然后在浏览器中访问 http://localhost:8080
"""

import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加 CORS 头，允许本地文件访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 检查必要文件是否存在
    required_files = ['index.html', 'app.js']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 错误：找不到文件 {file}")
            print("请确保所有文件都在同一目录下")
            sys.exit(1)
    
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print("🚀 Astro 文章编辑器服务器启动成功！")
            print(f"📍 服务器地址: http://localhost:{PORT}")
            print("🌐 正在打开浏览器...")
            print("💡 按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            webbrowser.open(f'http://localhost:{PORT}')
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试关闭其他服务或修改端口号")
        else:
            print(f"❌ 启动服务器时出错: {e}")

if __name__ == "__main__":
    main()

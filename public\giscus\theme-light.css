/*! MIT License
 * Copyright (c) 2018 GitHub Inc.
 * https://github.com/primer/primitives/blob/main/LICENSE
 */

 main {
  /* Custom Theme Colors */
  --secondary-color: oklch(40% 0.005 298);
  --secondary-color-25: oklch(40% 0.005 298 / 25%);
  --secondary-color-5: oklch(40% 0.005 298 / 5%);
  --background-color: oklch(96% 0.005 298);

  --color-fg-default: var(--secondary-color);
  --color-canvas-default: transparent;
  --color-canvas-overlay: var(--background-color);
  --color-canvas-inset: transparent;
  --color-canvas-subtle: transparent;
  --color-accent-fg: oklch(48.8% 0.243 264.376);
  --color-accent-emphasis: oklch(48.8% 0.243 264.376);

  /* Official Theme Colors */
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-btn-text: #24292f;
  --color-btn-bg: #f6f8fa;
  --color-btn-border: rgb(31 35 40 / 15%);
  --color-btn-shadow: 0 0 transparent;
  --color-btn-inset-shadow: 0 0 transparent;
  --color-btn-hover-bg: #f3f4f6;
  --color-btn-hover-border: rgb(31 35 40 / 15%);
  --color-btn-active-bg: hsl(220deg 14% 93% / 100%);
  --color-btn-active-border: rgb(31 35 40 / 15%);
  --color-btn-selected-bg: hsl(220deg 14% 94% / 100%);
  --color-btn-primary-text: #fff;
  --color-btn-primary-bg: #1f883d;
  --color-btn-primary-border: rgb(31 35 40 / 15%);
  --color-btn-primary-shadow: 0 0 transparent;
  --color-btn-primary-inset-shadow: 0 0 transparent;
  --color-btn-primary-hover-bg: #1a7f37;
  --color-btn-primary-hover-border: 0 0 transparent;
  --color-btn-primary-selected-bg: hsl(137deg 66% 28% / 100%);
  --color-btn-primary-selected-shadow: 0 0 transparent;
  --color-btn-primary-disabled-text: rgb(*********** / 80%);
  --color-btn-primary-disabled-bg: #94d3a2;
  --color-btn-primary-disabled-border: rgb(31 35 40 / 15%);
  --color-action-list-item-default-hover-bg: rgb(208 215 222 / 32%);
  --color-segmented-control-bg: transparent;
  --color-segmented-control-button-bg: transparent;
  --color-segmented-control-button-selected-border: #8c959f;
  --color-fg-muted: #656d76;
  --color-fg-subtle: #6e7781;
  --color-border-default: #d0d7de;
  --color-border-muted: hsl(210deg 18% 87% / 100%);
  --color-neutral-muted: rgb(175 184 193 / 20%);
  --color-accent-muted: rgb(84 174 255 / 40%);
  --color-accent-subtle: #ddf4ff;
  --color-success-fg: #1a7f37;
  --color-attention-fg: #9a6700;
  --color-attention-muted: rgb(212 167 44 / 40%);
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #d1242f;
  --color-danger-muted: rgb(255 129 130 / 40%);
  --color-danger-subtle: #ffebe9;
  --color-primer-shadow-inset: 0 0 transparent;
  --color-scale-gray-1: #eaeef2;
  --color-scale-blue-1: #b6e3ff;

  /*! Extensions from @primer/css/alerts/flash.scss */
  --color-social-reaction-bg-hover: var(--color-scale-gray-1);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-1);
}

main .pagination-loader-container {
  background-image: url("https://github.com/images/modules/pulls/progressive-disclosure-line-dark.svg");
}

main .gsc-loading-image {
  background-image: url("https://github.githubassets.com/images/mona-loading-dark.gif");
}

/* Smooth Font */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* All Border Color */
*:not(.BtnGroup-item) {
  border-color: var(--secondary-color-25)!important;
}

/* Hide Giscus Copyright and Avatar Line */
.gsc-tl-line,
.gsc-left-header em {
  display: none;
}

/* Font Size and Color on Comments Count */
h4.gsc-comments-count-separator,
h4.gsc-replies-count {
  color: var(--secondary-color);
  font-size: 0.875rem;
}

/* "Oldest" and "Newest" Buttons Style */
.BtnGroup-item,
.BtnGroup-item.BtnGroup-item--selected {
  background-color: transparent;
}
.BtnGroup-item.BtnGroup-item--selected {
  border-color: var(--secondary-color-25)!important;
}
.BtnGroup-item:not(.BtnGroup-item--selected):hover {
  border-color: var(--secondary-color-25)!important;
}
.BtnGroup-item button.btn:hover {
  background-color: var(--background-color);
}
.BtnGroup-item--selected .btn {
  font-weight: 500;
}
.BtnGroup-item .btn {
  background-color: transparent;
}

/* Larger Padding Top on Author Comment Header */
.gsc-comment-header {
  padding-top: 1rem;
}

/* Add Padding Bottom on Replies Box */
.gsc-replies {
  padding-bottom: 0.5rem;
}

/* Hide Underline on Reply Time */
.gsc-comment-author a:has(time),
.gsc-reply-author a:has(time) {
  text-decoration: none;
  font-size: 0.75rem;
}

/* Hide Underline on Comments Count */
.gsc-comments-count a {
  text-decoration: none!important;
}

/* Social Reactions Icons */
.gsc-social-reaction-summary-item.has-reacted {
  background-color: var(--background-color);
}
.gsc-social-reaction-summary-item.has-reacted:hover {
  background-color: var(--background-color)!important;
  border-color: var(--color-accent-fg)!important;
}
.color-bg-info {
  background-color: var(--background-color);
}

/* Medium Font Weight on Comment Box Tabs Selected Button  */
.gsc-comment-box-tabs .color-text-primary {
  font-weight: 500;
}

/* Thinner Bottom Line on Comment Box Preview Border */
.gsc-comment-box-preview {
  border-bottom-width: 1px;
}

/* Hide Underline on sign out button */
.link-secondary {
  text-decoration: none;
}

/* Scrollbar Style on Code Blocks */
pre {
  background-color: var(--secondary-color-5)!important;
  scrollbar-width: thin!important;
  scrollbar-color: var(--secondary-color-25) transparent!important;
}

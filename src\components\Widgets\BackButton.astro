---
import GoBackIcon from '@/assets/icons/go-back.svg';
---

<button
  id="back-button"
  class="hidden"
  lg="absolute left--10 top-3.8 block aspect-square w-4.5 c-secondary/40 transition-colors ease-out hover:c-secondary/80 active:scale-90!"
  aria-label="Go back"
>
  <GoBackIcon
    aria-hidden="true"
    fill="currentColor"
  />
</button>

<!-- Go Back Script >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> -->
<script>
function handleBackButtonClick(e: MouseEvent) {
  const target = e.target instanceof Element ? e.target : null
  const button = target?.closest('#back-button')

  if (!button) {
    return
  }

  // Navigate back if history exists
  if (window.history.length > 1) {
    window.history.back()
    return
  }

  // Fallback to homepage
  const siteTitleLink = document.getElementById('site-title-link')
  if (siteTitleLink) {
    siteTitleLink.click()
  }
}

document.addEventListener('click', handleBackButtonClick, { passive: true })
</script>

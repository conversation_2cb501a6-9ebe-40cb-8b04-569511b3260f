/*! MIT License
 * Copyright (c) 2018 GitHub Inc.
 * https://github.com/primer/primitives/blob/main/LICENSE
 */

 main {
  /* Custom Theme Colors */
  --secondary-color: oklch(77% 0.005 298);
  --secondary-color-25: oklch(77% 0.005 298 / 25%);
  --secondary-color-5: oklch(77% 0.005 298 / 5%);
  --background-color: oklch(22% 0.005 298);

  --color-fg-default: var(--secondary-color);
  --color-canvas-default: transparent;
  --color-canvas-overlay: var(--background-color);
  --color-canvas-inset: transparent;
  --color-canvas-subtle: transparent;
  --color-accent-fg: oklch(70.7% 0.165 254.624);
  --color-accent-emphasis: oklch(70.7% 0.165 254.624);

  /* Official Theme Colors */
  --color-prettylights-syntax-comment: #8b949e;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-variable: #ffa657;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-italic: #c9d1d9;
  --color-prettylights-syntax-markup-bold: #c9d1d9;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-btn-text: #c9d1d9;
  --color-btn-bg: #21262d;
  --color-btn-border: rgb(*********** / 10%);
  --color-btn-shadow: 0 0 transparent;
  --color-btn-inset-shadow: 0 0 transparent;
  --color-btn-hover-bg: #30363d;
  --color-btn-hover-border: #8b949e;
  --color-btn-active-bg: hsl(212deg 12% 18% / 100%);
  --color-btn-active-border: #6e7681;
  --color-btn-selected-bg: #161b22;
  --color-btn-primary-text: #fff;
  --color-btn-primary-bg: #238636;
  --color-btn-primary-border: rgb(*********** / 10%);
  --color-btn-primary-shadow: 0 0 transparent;
  --color-btn-primary-inset-shadow: 0 0 transparent;
  --color-btn-primary-hover-bg: #2ea043;
  --color-btn-primary-hover-border: 0 0 transparent;
  --color-btn-primary-selected-bg: #238636;
  --color-btn-primary-selected-shadow: 0 0 transparent;
  --color-btn-primary-disabled-text: rgb(*********** / 50%);
  --color-btn-primary-disabled-bg: rgb(35 134 54 / 60%);
  --color-btn-primary-disabled-border: rgb(*********** / 10%);
  --color-action-list-item-default-hover-bg: rgb(177 186 196 / 12%);
  --color-segmented-control-bg: transparent;
  --color-segmented-control-button-bg: transparent;
  --color-segmented-control-button-selected-border: #6e7681;
  --color-fg-muted: #7d8590;
  --color-fg-subtle: #6e7681;
  --color-border-default: #30363d;
  --color-border-muted: #21262d;
  --color-neutral-muted: rgb(110 118 129 / 40%);
  --color-accent-muted: rgb(56 139 253 / 40%);
  --color-accent-subtle: rgb(56 139 253 / 10%);
  --color-success-fg: #3fb950;
  --color-attention-fg: #d29922;
  --color-attention-muted: rgb(187 128 9 / 40%);
  --color-attention-subtle: rgb(187 128 9 / 15%);
  --color-danger-fg: #f85149;
  --color-danger-muted: rgb(248 81 73 / 40%);
  --color-danger-subtle: rgb(248 81 73 / 10%);
  --color-primer-shadow-inset: 0 0 transparent;
  --color-scale-gray-7: #21262d;
  --color-scale-blue-8: #0c2d6b;

  /*! Extensions from @primer/css/alerts/flash.scss */
  --color-social-reaction-bg-hover: var(--color-scale-gray-7);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-8);
}

main .pagination-loader-container {
  background-image: url("https://github.com/images/modules/pulls/progressive-disclosure-line-dark.svg");
}

main .gsc-loading-image {
  background-image: url("https://github.githubassets.com/images/mona-loading-dark.gif");
}

/* Smooth Font */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* All Border Color */
*:not(.BtnGroup-item) {
  border-color: var(--secondary-color-25)!important;
}

/* Hide Giscus Copyright and Avatar Line */
.gsc-tl-line,
.gsc-left-header em {
  display: none;
}

/* Font Size and Color on Comments Count */
h4.gsc-comments-count-separator,
h4.gsc-replies-count {
  color: var(--secondary-color);
  font-size: 0.875rem;
}

/* "Oldest" and "Newest" Buttons Style */
.BtnGroup-item,
.BtnGroup-item.BtnGroup-item--selected {
  background-color: transparent;
}
.BtnGroup-item.BtnGroup-item--selected {
  border-color: var(--secondary-color-25)!important;
}
.BtnGroup-item:not(.BtnGroup-item--selected):hover {
  border-color: var(--secondary-color-25)!important;
}
.BtnGroup-item button.btn:hover {
  background-color: transparent;
}
.BtnGroup-item--selected .btn {
  font-weight: 500;
}
.BtnGroup-item .btn {
  background-color: transparent;
}

/* Larger Padding Top on Author Comment Header */
.gsc-comment-header {
  padding-top: 1rem;
}

/* Add Padding Bottom on Replies Box */
.gsc-replies {
  padding-bottom: 0.5rem;
}

/* Hide Underline on Reply Time */
.gsc-comment-author a:has(time),
.gsc-reply-author a:has(time) {
  text-decoration: none;
  font-size: 0.75rem;
}

/* Hide Underline on Comments Count */
.gsc-comments-count a {
  text-decoration: none!important;
}

/* Social Reactions Icons */
.gsc-social-reaction-summary-item.has-reacted {
  background-color: var(--background-color);
}
.gsc-social-reaction-summary-item.has-reacted:hover {
  background-color: var(--background-color)!important;
  border-color: var(--color-accent-fg)!important;
}
.color-bg-info {
  background-color: var(--background-color);
}

/* Medium Font Weight on Comment Box Tabs Selected Button  */
.gsc-comment-box-tabs .color-text-primary {
  font-weight: 500;
}

/* Thinner Bottom Line on Comment Box Preview Border */
.gsc-comment-box-preview {
  border-bottom-width: 1px;
}

/* Hide Underline on sign out button */
.link-secondary {
  text-decoration: none;
}

/* Scrollbar Style on Code Blocks */
pre {
  background-color: var(--secondary-color-5)!important;
  scrollbar-width: thin!important;
  scrollbar-color: var(--secondary-color-25) transparent!important;
}

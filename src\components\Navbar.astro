---
import { ui } from '@/i18n/ui'
import { getPageInfo } from '@/utils/page'

const { currentLang, isHome, isPost, isTag, isMemos, isFriends, isAbout, getLocalizedPath } = getPageInfo(Astro.url.pathname)
const currentUI = ui[currentLang as keyof typeof ui] ?? {}

const isPostActive = isHome || isPost
const isTagActive = isTag
const isMemosActive = isMemos
const isFriendsActive = isFriends
const isAboutActive = isAbout

function getNavItemClass(isActive: boolean) {
  return isActive
    ? 'highlight-static c-primary font-bold after:bottom-0.7em'
    : 'highlight-hover transition-[colors,font-weight] after:bottom-0.7em hover:(c-primary font-bold)'
}

const navItems = [
  {
    href: '/',
    label: currentUI.posts,
    className: getNavItemClass(isPostActive),
  },
  {
    href: '/tags/',
    label: currentUI.tags,
    className: getNavItemClass(isTagActive),
  },
  {
    href: "/memos/",
    label: currentUI.memos,
    className: getNavItemClass(isMemosActive),
  },
  {
    href: '/friends/',
    label: currentUI.friends,
    className: getNavItemClass(isFriendsActive),
  },
  {
    href: '/about/',
    label: currentUI.about,
    className: getNavItemClass(isAboutActive),
  },
]
---

<nav
  aria-label="Site Navigation"
  class:list={[
    isPost ? 'hidden lg:block' : '',
    'mb-10.5 text-3.6 font-semibold leading-2.45em font-navbar',
    'lg:(uno-desktop-column text-4 bottom-[min(9.04rem+3.85vw,12.5rem)]) cjk:tracking-0.02em',
  ]}
>
  <ul lg="flex-col items-start text-4" flex="~ row gap-2 justify-center">
    {navItems.map(item => (
      <li>
        <a
          href={getLocalizedPath(item.href)}
          class={item.className}
        >
          {item.label}
        </a>
      </li>
    ))}
  </ul>
</nav>

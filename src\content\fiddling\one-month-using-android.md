---
title: 迁移安卓一月谈
lang: zh
published: 2025-06-05T23:26:00.000+08:00
tags: ["折腾","Android","Oppo","苹果","Apple","手机"]
abbrlink: fiddling/one-month-using-android
description: "换机频繁的我，经历了从一加到 iPhone 的转变，逐渐从折腾的乐趣转向了对生态的依赖。最近在 npy 的建议下，入手了 Oppo Find X8 Ultra，主要是为了提升拍照体验。尽管沉浸在 Apple 的生态中，但 App 的迁移问题让我意识到，Android 的应用生态依然参差不齐，寻找替代品的过程颇具挑战。这个月的迁移经历让我感受到不同平台之间的摩擦与适应。"
---
### 前言

我是一个换机很频繁的人，尤其是工作有了一些积蓄后，更是养成了一点“喜新厌旧”的坏毛病，一部手机在我手上很难待过一年。一旦有新机发售，就开始盘算着买一部做主力机如何如何，现有的软件/工具链如何迁移过去，想着想着就开始手痒，于是下单换机，旧机或是退给家人使用，或是卖二手，或是留在身边当备用机，然后被慢慢遗忘。

毕业后这四年起，仅仅主力机就有这些：一加 8T、一加 9Pro、Pixel 5、Oppo Find X6 Pro、Vivo X Fold3 Pro、iPhone 14 Pro、iPhone 16 Pro，另外还有一部一加 7Pro 作为备用机。

从换机历程，大概能看出我玩机心态的变化 —— 最初的一加系、pixel，早年刚毕业的时候喜欢折腾，类原生、root、解 bootloader，怎么自由怎么来；到 oppo、vivo，逐渐放弃了折腾，但还是保留了一些玩机的心态，如 vivo 的折叠屏；再到 iPhone，彻底投入 Apple 阵营，和 Apple 生态的深度融合。

正沉溺 Apple 构筑的生态链中不愿自拔时，npy 给了我当头一棒：“你这拍照也太垃圾了”。也是，早年玩机，主要关注跑分性能之类的纸面参数，摄像头嘛，能扫码就行。现在，我要向她证明：

> 一切手法上的不足，都可以通过软硬件不足！

当然这又是我换机的一个借口罢了，事实是，在上次 [关西行](/travels/kansai-202504) 中，K 君的相机确实引起了我很大的兴趣，尤其是那大长焦。但我天生惫懒，不愿后期修图，那就不如买一部镜头和调色稍微好些的<mark>手机</mark>，来满足一下我的拍照瘾吧。

恰好上半年御三家都发布了各自的 Ultra 超大杯：Vivo X200 Ultra、Oppo Find X8 Ultra、小米 15 Ultra。首先排除小米（<del>雷军！金凡！</del>），vivo 的长焦增距确实很吸引我，简直演唱会神器，但鉴于我抢不到演唱会的门票，最终还是入手了主摄有一英寸大底的 Oppo Find X8 Ultra。截止目前已经换机有一个月了，迁移最主要的麻烦就是从 Apple 生态链中迁移过来，尤其是很多 App 开发者只开发 iOS 版，Android 版的 App 生态又是良莠不齐，寻找平替花了不少时间。由于无法解锁 BootLoader，root 就成了奢望，很多功能只能委屈一下了。

### 去广告

更换到国产安卓生态的最大问题，就是无处不在的广告。上手第一步，去广告：

<iframe src="//player.bilibili.com/player.html?isOutside=true&aid=113746622021969&bvid=BV18c6JYLEmw&cid=27626637570&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>

完成后，就只剩下天气应用的二级页面下方的推荐广告去不掉了，和牛皮癣一样。

### 照片备份

此前由于使用的完整的苹果生态，所有的照片都在 iCloud 上，为此也开了 iCloud 200G 扩容，专用于存储照片，目前使用了 50G，包含了从本科开始的所有照片。前些日子把 NAS 更换为了飞牛系统，这些照片又同步备份了一份到飞牛自带的相册。

这次切换到安卓，品牌方自带的云肯定是不用的（说的就是你，欢太云），更换到 Google 相册的话，有比较麻烦。去年花了不少时间把相册从 Google Photos 导出到 iCloud，现在又导回去算个什么事情？另外 Google 云空间不开会员只有 15GB，虽然用校友邮箱薅了 Google AI Pro 的 2T 羊毛，但心里总是不踏实。

由于我的其他设备还是苹果，最终决定还是继续使用 iCloud 存储。Mac 上安装一个 O+ 互联，每天打开电脑是自动连接，就是需要手动导出再导入到 Apple 相册。说实话 O+ 互联的体验比 V 家的办公套件要差很多，不仅 Bug 多，功能还不完善：剪贴板无法同步、不支持 Mac 控制手机、无法自动同步照片，看 Oppo 那边也摆烂了，更新也不怎么勤快，凑活着用吧。

### 代理/回家

我的手机和电脑上都安装了代理软件，主要就是用于回家，即访问家里的网络环境，原因有二：
1. 访问家里部署的服务，比如上面提到的相册和笔记服务
2. 家中部署了 DNS 去广告和基于规则的透明代理，只要能连接回家，就无须再配置复杂的规则，享受和在家里一样的网络环境

之前使用 iPhone 时，Surge 我是 24 小时挂在后台的，全局回家。但如果人在家中时，还挂着全局连接家中的代理，会导致上不了网 Surge 有个很重要的功能，叫子网覆盖，可以根据当前连接的 WIFI SSID，执行一些动作，比如 SUSPEND 全局绕过代理。这样就完美解决了家中挂代理无法上网的问题，Surge 可以全程保持在后台了。

切换到安卓阵营，首先面临的问题就是选择太多，不同于 iOS 大部分都是自己实现的代理核心，安卓大都是直接复用开源的核心，如 mihomo（clash）、v2ray、xray 或比较新的 singbox，基于这些核心封装 UI 界面进而封装客户端，因此<del>大部分</del>几乎所有客户端都无法在 UI 上直接调整规则配置，只能通过上传或订阅配置文件。这倒是件小事，基于我的需求，配置文件一旦写成便几乎不会再变动。比较麻烦的是，很难找到一个客户端支持类似于 Surge 的子网覆盖功能，我可不想离家回家都得手动开关一次代理。

最终找到了一个比较小众的 SurfBoard，支持 SSID 规则，直接在规则最顶端增加一条 SSID 规则走 DIRECT，也算曲线救国实现了功能。更神奇的是，这个 App 兼容 Surge 格式的配置文件。

::github{repo="getsurfboard/surfboard"}

虽然 SurfBoard 默认是 FakeIP 无法修改，即使走 SSID 规则请求也会首先解析成 FakeIP，不如全局绕过来得更加彻底，但是聊胜于无吧。

### 记账

换机之前恰好养成了记账的习惯，至今记账也两月有余了，除了日常用钱时的记账之外，还会每天对账记录理财收益。iOS 上有个记账软件很推荐，叫 iCost，界面简洁但功能丰富，支持将快捷记账添加为系统捷径，可以通过敲击两下背板或者 iPhone 16 的快捷按钮唤醒，自动 AI 识屏并填写主要信息，全程只需要填一个分类即可。iCost 也有开发安卓端的规划，但是规划了两年多，进度缓慢。

小红书上搞记账软件的也很多，国人独立开发三大件：笔记、TODO 和记账。最终找到了一个比较老牌的记账软件：钱迹，开发时间似乎比 iCost 还长，并且支持多平台同步：iOS、MacOS、Android 甚至 Windows、鸿蒙 Next 都有支持，历史账单也可以很快捷地从 iCost 导入。该有的功能也一个不拉：资产管理、信用卡还款、退款、、多币种、多账本（虽然这个我用不到）。放个 [主页](https://qianjiapp.com)。

钱迹的自动记账感觉比 iCost 要好，不过或许是平台不同带来的差异。钱迹的自动记账基于的是系统的无障碍功能，在识别到当前打开的窗口是账单页面（支付宝或者微信）时，就会自动识别并弹出小窗快速记账。不过界面识别的准确度有待提升，有时从其他软件跳转支付宝付款完成的界面识别会失败，只能手动打开支付宝的支付记录中再识别一次，不过小瑕疵，能接受。

### 日历/TODO

iOS 18 最大的更新，就是打通了日历和提醒事项，在日历上可以直观查看和操作所有提醒事项，得益于 iOS 优秀的通知推送能力，提醒也十分及时，几乎算得上是 iOS 上最提升生产力的小工具了，比不少付费软件做的都好。切换到安卓后，首先排除的就是系统自带日历，完全不具备可迁移性，下一次换机一旦换一个品牌，数据就要全丢失了。

找来找去，中途微软的 Outlook 比较符合要求。作为一个邮件 app，日历功能做的出奇得好用。就是无法显示农历，只能通过订阅一些农历日历来展示，这样每天都会显示一些日程，由于 Outlook 本地化做的也不咋的，传统节日、节假日调休一概没有，又得多订阅一些日程，再加上农历生日、纪念日，整个日历一眼望去满满当当，眼花缭乱，哪一天真的有事情要做都没法直观看到。另外 Todo 功能薄弱，只能达到凑活用的水平。

无意中想到一个老牌的 Todo 软件：滴答清单，于是下载下来体验了一番，没想到就给我找到了 iOS 日历 + Todo 的最佳平替。上面提到的所有功能都直接支持：农历显示、农历日程、节假日调休、倒数日/纪念日、Todo 展示。不仅如此，我在 iOS Todo 上还得手动划分列表支持的四象限法，滴答清单直接原生支持，意外之喜啊。就是 139 元一年的会员价格有些不美了，不过这是我的问题。

### Watch

切换到安卓生态后，终于摆脱了美丽废物 —— Apple Watch，终于不用一天充一次电了。用了 Oppo 的手机，自然要用 Oppo 的手表。购入了 Oppo Watch X2，几乎和一部全新的 Apple Watch 差不多贵了。

Oppo Watch X2 采用了现在智能手表比较少的圆形表盘，表盘样式也多是拟物主题，令人眼前一亮。我对手表的要求很低，能看时间、同步手机通知、同步手机闹钟、记录睡眠数据和其他健康数据即可。这款手表都能完美地解决，另外比较惊喜的是 HRV，这个在 Apple Watch 上基本被忽略掉、大部分情况下只能通过第三方 app 来展示提示的数据，在 Oppo 上得到了空前的重视，有一个专门的身心状态页面，还请来了孙颖莎代言。

### 尾声

折腾了那么多，终于是有一部用着舒服的手机了，拍照也得到了 npy 的认可，完美~
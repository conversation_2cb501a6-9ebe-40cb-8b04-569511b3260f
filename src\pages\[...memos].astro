---
import { defaultLocale, moreLocales } from "@/config";
import Layout from "@/layouts/Layout.astro";
import Memos from "@/components/Memos.astro";

export async function getStaticPaths() {
  type PathItem = {
    params: { memos: string };
    props: { lang: string };
  };

  const paths: PathItem[] = [];

  // Default locale
  paths.push({
    params: { memos: "memos/" },
    props: { lang: defaultLocale },
  });

  // More locales
  moreLocales.forEach((lang: string) => {
    paths.push({
      params: { memos: `${lang}/memos/` },
      props: { lang },
    });
  });

  return paths;
}
---

<Layout>
  <article class="heti mb-12.6">
    <Memos />
  </article>
</Layout>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astro 文章编辑器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: #fff;
            border-bottom: 1px solid #e1e4e8;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #24292e;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            background: #fff;
            color: #24292e;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s;
        }

        .btn:hover {
            background: #f6f8fa;
        }

        .btn-primary {
            background: #0366d6;
            color: #fff;
            border-color: #0366d6;
        }

        .btn-primary:hover {
            background: #0256cc;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 250px;
            background: #fff;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
        }

        .template-section {
            padding: 1rem;
            border-bottom: 1px solid #e1e4e8;
        }

        .template-section h3 {
            margin-bottom: 0.5rem;
            color: #24292e;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .template-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.2s;
        }

        .template-item:hover {
            background: #f6f8fa;
            border-color: #0366d6;
        }

        .editor-container {
            flex: 1;
            display: flex;
        }

        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #f6f8fa;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e1e4e8;
            font-weight: 600;
            font-size: 0.875rem;
            color: #24292e;
        }

        .CodeMirror {
            height: 100% !important;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .preview-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #e1e4e8;
        }

        .preview-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
            background: #fff;
        }

        .markdown-body {
            max-width: none;
        }

        .frontmatter-preview {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 8px;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 600;
            color: #24292e;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5da;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }

        .status-bar {
            background: #f6f8fa;
            border-top: 1px solid #e1e4e8;
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            color: #586069;
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Astro 文章编辑器</h1>
        <div class="header-actions">
            <button class="btn" onclick="newArticle()">📝 新建文章</button>
            <button class="btn" onclick="loadFile()">📂 打开文件</button>
            <button class="btn btn-primary" onclick="saveFile()">💾 保存文件</button>
            <button class="btn" onclick="exportFile()">📤 导出</button>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="template-section">
                <h3>📄 文章模板</h3>
                <div class="template-item" onclick="loadTemplate('blog')">
                    📝 博客文章
                </div>
                <div class="template-item" onclick="loadTemplate('tech')">
                    💻 技术文章
                </div>
                <div class="template-item" onclick="loadTemplate('travel')">
                    ✈️ 旅行文章
                </div>
                <div class="template-item" onclick="loadTemplate('about')">
                    👤 关于页面
                </div>
            </div>

            <div class="template-section">
                <h3>🎨 语言版本</h3>
                <div class="template-item" onclick="setLanguage('')">
                    🌍 通用版本
                </div>
                <div class="template-item" onclick="setLanguage('zh')">
                    🇨🇳 简体中文
                </div>
                <div class="template-item" onclick="setLanguage('en')">
                    🇺🇸 English
                </div>
                <div class="template-item" onclick="setLanguage('ja')">
                    🇯🇵 日本語
                </div>
                <div class="template-item" onclick="setLanguage('zh_tw')">
                    🇹🇼 繁體中文
                </div>
            </div>

            <div class="template-section">
                <h3>🔧 快捷操作</h3>
                <div class="template-item" onclick="insertImage()">
                    🖼️ 插入图片
                </div>
                <div class="template-item" onclick="insertLink()">
                    🔗 插入链接
                </div>
                <div class="template-item" onclick="insertCode()">
                    💻 插入代码
                </div>
                <div class="template-item" onclick="insertTable()">
                    📊 插入表格
                </div>
            </div>
        </div>

        <div class="editor-container">
            <div class="editor-panel">
                <div class="panel-header">📝 Markdown 编辑器</div>
                <textarea id="editor"></textarea>
            </div>

            <div class="preview-panel">
                <div class="panel-header">👁️ 实时预览</div>
                <div class="preview-content">
                    <div id="preview" class="markdown-body"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <span id="status">就绪</span>
        <span id="stats">行数: 0 | 字符: 0</span>
    </div>

    <!-- 新建文章模态框 -->
    <div id="newArticleModal" class="modal">
        <div class="modal-content">
            <h3>📝 新建文章</h3>
            <form id="newArticleForm">
                <div class="form-group">
                    <label for="articleTitle">标题 *</label>
                    <input type="text" id="articleTitle" required>
                </div>
                <div class="form-group">
                    <label for="articleDescription">描述</label>
                    <textarea id="articleDescription" placeholder="文章简短描述，用于SEO"></textarea>
                </div>
                <div class="form-group">
                    <label for="articleTags">标签</label>
                    <input type="text" id="articleTags" placeholder="用逗号分隔，如：技术,前端,JavaScript">
                </div>
                <div class="form-group">
                    <label for="articleLang">语言</label>
                    <select id="articleLang">
                        <option value="">通用版本</option>
                        <option value="zh">简体中文</option>
                        <option value="en">English</option>
                        <option value="ja">日本語</option>
                        <option value="zh_tw">繁體中文</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="articleType">文章类型</label>
                    <select id="articleType">
                        <option value="blog">博客文章</option>
                        <option value="tech">技术文章</option>
                        <option value="travel">旅行文章</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" onclick="closeModal('newArticleModal')">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>

    <input type="file" id="fileInput" accept=".md,.mdx" style="display: none;">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/markdown/markdown.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/yaml/yaml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/yaml-frontmatter/yaml-frontmatter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    <script src="app.js"></script>
</body>
</html>

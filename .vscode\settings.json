{
  "[markdown]": {
    "editor.quickSuggestions": {
      "comments": "on",
      "other": "on",
      "strings": "on"
    }
  },
  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off", "fixable": true },
    { "rule": "format/*", "severity": "off", "fixable": true },
    { "rule": "*-indent", "severity": "off", "fixable": true },
    { "rule": "*-spacing", "severity": "off", "fixable": true },
    { "rule": "*-spaces", "severity": "off", "fixable": true },
    { "rule": "*-order", "severity": "off", "fixable": true },
    { "rule": "*-dangle", "severity": "off", "fixable": true },
    { "rule": "*-newline", "severity": "off", "fixable": true },
    { "rule": "*quotes", "severity": "off", "fixable": true },
    { "rule": "*semi", "severity": "off", "fixable": true }
  ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],

  "typescript.tsdk": "node_modules/typescript/lib",
  "files.associations": {
    "*.mdx": "markdown"
  },
  "cSpell.words": [
    "abbrlink",
    "antfu",
    "apiflash",
    "apos",
    "astrojs",
    "attributify",
    "autocorrect",
    "backref",
    "bbbi",
    "bilibili",
    "bitdepth",
    "Blockquotes",
    "brackethighlighter",
    "bvid",
    "canvaskit",
    "Cpath",
    "Csvg",
    "dhakar",
    "Disqus",
    "esno",
    "Español",
    "framespacing",
    "Français",
    "Frontmatter",
    "Fuwari",
    "Giscus",
    "gtag",
    "heti",
    "katex",
    "Lightbox",
    "lokesh",
    "lqip",
    "mdast",
    "msrc",
    "msvalidate",
    "ndarray",
    "nocss",
    "noopener",
    "noreferrer",
    "Noto",
    "Oklab",
    "oklch",
    "partytown",
    "playbtn",
    "prettylights",
    "qwik",
    "radishzz",
    "radishzzz",
    "rehype",
    "reimagines",
    "Retipografía",
    "retypeset",
    "Roundhand",
    "Segoe",
    "shiki",
    "srcset",
    "STIX",
    "stylesheet",
    "sublimelinter",
    "Twikoo",
    "umami",
    "unocss",
    "Vercel",
    "videoid",
    "vite",
    "waline",
    "webp",
    "Переверстка",
    "Русский"
  ]
}
